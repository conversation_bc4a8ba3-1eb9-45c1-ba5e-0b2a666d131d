/* stylelint-disable scss/percent-placeholder-pattern */
%ResetBorder {
  border: 0;
  box-shadow: none;
}

%CompWidth {
  & > .ant-input,
  & > .ant-input-number,
  & > .ant-select,
  & > .ant-cascader-picker,
  & > .ant-calendar-picker,
  & > .ant-time-picker {
    width: 100%;
  }
}

.vxe-form {
  .vxe-form--item-content {
    @extend %CompWidth;
  }
}

.vxe-table--filter-antd-wrapper {
  & > .ant-input,
  & > .ant-input-number,
  & > .ant-select,
  & > .ant-rate {
    width: 180px;
  }
}

.vxe-cell,
.vxe-tree-cell {
  @extend %CompWidth;

  & > .ant-rate {
    vertical-align: bottom;

    .anticon-star {
      display: block;
    }
  }
}

.col--valid-error {
  & > .vxe-cell,
  & > .vxe-tree-cell {
    & > .ant-input,
    & > .ant-select .ant-input,
    & > .ant-select .ant-select-selection,
    & > .ant-input-number,
    & > .ant-cascader-picker .ant-cascader-input,
    & > .ant-calendar-picker .ant-calendar-picker-input {
      box-shadow: none;
    }
  }
}

.vxe-table.cell--highlight {
  .vxe-cell,
  .vxe-tree-cell {
    & > .ant-input,
    & > .ant-input-number {
      @extend %ResetBorder;

      padding: 0;
    }

    & > .ant-select {
      .ant-input {
        @extend %ResetBorder;

        padding: 0;
      }

      .ant-select-selection {
        @extend %ResetBorder;

        .ant-select-selection__rendered {
          margin: 0;
        }
      }
    }

    & > .ant-input-number {
      .ant-input-number-input {
        padding: 0;
      }

      .ant-input-number-handler-wrap,
      .ant-input-number-handler-down {
        @extend %ResetBorder;
      }
    }

    & > .ant-cascader-picker {
      .ant-input {
        @extend %ResetBorder;
      }

      .ant-cascader-picker-label {
        padding: 0;
      }
    }

    & > .ant-calendar-picker {
      .ant-calendar-picker-input {
        @extend %ResetBorder;

        padding: 0;
      }
    }

    & > .ant-time-picker {
      .ant-time-picker-input {
        @extend %ResetBorder;

        padding: 0;
      }
    }
  }
}
