// token key
export const TOKEN_KEY = 'TOKEN__';

export const LOCALE_KEY = 'LOCALE__';

// user info key
export const USER_INFO_KEY = 'USER__INFO__';

// role info key
export const ROLES_KEY = 'ROLES__KEY__';

// project config key
export const PROJ_CFG_KEY = 'PROJ__CFG__KEY__';

// api address
export const API_ADDRESS = 'API_ADDRESS__';

// lock info
export const LOCK_INFO_KEY = 'LOCK__INFO__KEY__';

export const MULTIPLE_TABS_KEY = 'MULTIPLE_TABS__KEY__';

export const APP_DARK_MODE_KEY = '__APP__DARK__MODE__';

// base global local key
export const APP_LOCAL_CACHE_KEY = 'COMMON__LOCAL__KEY__';

// base global session key
export const APP_SESSION_CACHE_KEY = 'COMMON__SESSION__KEY__';

// login enterpriseName local session key
export const ENTERPRISE_NAME_SESSION_CACHE_KEY = 'ENTERPRISE_NAME__SESSION__KEY';

// login userName local session key
export const USER_NAME_SESSION_CACHE_KEY = 'USER_NAME__SESSION__KEY';

// login password local session key
export const PASSWORD_SESSION_CACHE_KEY = 'PASSWORD__SESSION__KEY';

// login rememberMe local session key
export const REMEMBER_ME_SESSION_CACHE_KEY = 'REMEMBER_ME__SESSION__KEY';

// table 列设置
export const TABLE_SETTING_KEY = 'TABLE__SETTING__KEY__';

export enum CacheTypeEnum {
  SESSION,
  LOCAL,
}
