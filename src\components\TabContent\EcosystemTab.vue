<template>
  <div class="w-full min-h-[650px] relative">
    <!-- 生态背景 -->
    <div class="absolute inset-0 bg-gradient-to-br from-orange-600 to-red-600"></div>

    <!-- 登录表单覆盖层 -->
    <LoginOverlay />

    <!-- 生态内容 -->
    <div class="relative flex items-center justify-center z-20 min-h-[650px]">
      <div class="max-w-6xl mx-auto px-8 text-center text-white">
        <h1 class="text-5xl font-bold mb-6">生态合作伙伴</h1>
        <p class="text-xl mb-12 text-orange-100">
          构建完整的跨境电商生态圈，与优质合作伙伴共创价值
        </p>
        
        <!-- 生态圈展示 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mt-12">
          <!-- 物流合作伙伴 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-8">
            <div class="w-20 h-20 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold mb-4">物流服务</h3>
            <p class="text-orange-100 mb-4">与全球顶级物流公司合作</p>
            <ul class="text-left text-orange-100 space-y-2">
              <li>• DHL全球快递</li>
              <li>• FedEx联邦快递</li>
              <li>• UPS联合包裹</li>
              <li>• 顺丰国际</li>
            </ul>
          </div>

          <!-- 支付合作伙伴 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-8">
            <div class="w-20 h-20 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold mb-4">支付服务</h3>
            <p class="text-orange-100 mb-4">安全便捷的支付解决方案</p>
            <ul class="text-left text-orange-100 space-y-2">
              <li>• PayPal</li>
              <li>• Stripe</li>
              <li>• 连连支付</li>
              <li>• Payoneer</li>
            </ul>
          </div>

          <!-- 平台合作伙伴 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-8">
            <div class="w-20 h-20 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold mb-4">电商平台</h3>
            <p class="text-orange-100 mb-4">覆盖全球主流电商平台</p>
            <ul class="text-left text-orange-100 space-y-2">
              <li>• Amazon</li>
              <li>• eBay</li>
              <li>• Shopify</li>
              <li>• AliExpress</li>
            </ul>
          </div>

          <!-- 数据服务 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-8">
            <div class="w-20 h-20 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold mb-4">数据分析</h3>
            <p class="text-orange-100 mb-4">专业的数据分析服务</p>
            <ul class="text-left text-orange-100 space-y-2">
              <li>• 市场趋势分析</li>
              <li>• 竞品监控</li>
              <li>• 用户行为分析</li>
              <li>• 销售预测</li>
            </ul>
          </div>

          <!-- 营销服务 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-8">
            <div class="w-20 h-20 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold mb-4">营销推广</h3>
            <p class="text-orange-100 mb-4">全方位营销推广服务</p>
            <ul class="text-left text-orange-100 space-y-2">
              <li>• Google Ads</li>
              <li>• Facebook广告</li>
              <li>• 网红营销</li>
              <li>• SEO优化</li>
            </ul>
          </div>

          <!-- 技术服务 -->
          <div class="bg-white/10 backdrop-blur-sm rounded-xl p-8">
            <div class="w-20 h-20 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold mb-4">技术支持</h3>
            <p class="text-orange-100 mb-4">专业的技术解决方案</p>
            <ul class="text-left text-orange-100 space-y-2">
              <li>• API接口服务</li>
              <li>• 系统集成</li>
              <li>• 定制开发</li>
              <li>• 技术咨询</li>
            </ul>
          </div>
        </div>

        <!-- 合作优势 -->
        <div class="mt-16 bg-white/5 rounded-2xl p-8">
          <h2 class="text-3xl font-bold mb-8">合作优势</h2>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
              <div class="text-4xl font-bold text-orange-300 mb-2">500+</div>
              <p class="text-orange-100">合作伙伴</p>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-orange-300 mb-2">100+</div>
              <p class="text-orange-100">国家覆盖</p>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-orange-300 mb-2">24/7</div>
              <p class="text-orange-100">技术支持</p>
            </div>
            <div class="text-center">
              <div class="text-4xl font-bold text-orange-300 mb-2">99.9%</div>
              <p class="text-orange-100">服务可用性</p>
            </div>
          </div>
        </div>

        <!-- 联系我们 -->
        <div class="mt-12">
          <button class="bg-white text-orange-600 font-semibold py-3 px-8 rounded-lg hover:bg-orange-50 transition-colors duration-200 mr-4">
            成为合作伙伴
          </button>
          <button class="border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-orange-600 transition-colors duration-200">
            了解更多
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LoginOverlay from '@/components/LoginOverlay/index.vue';
// 生态组件逻辑
</script>

<style scoped>
/* 生态组件样式 */
</style>
