<template>
  <footer class="bg-gray-700 text-gray-300 py-8">
    <div class="max-w-6xl mx-auto px-8">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- 网站信息 -->
        <div>
          <h3 class="text-white font-medium mb-4">网站导航</h3>
          <ul class="space-y-2 text-sm">
            <li>TEMU选品</li>
            <li>数据监控</li>
          </ul>
        </div>

        <!-- 联系我们 -->
        <div>
          <h3 class="text-white font-medium mb-4">联系我们</h3>
          <div class="space-y-2 text-sm">
            <p>客服热线：15554292779</p>
            <p>地址：广东省广州市海珠区大江南街道环城中路316-318号</p>
            <p>邮箱：<EMAIL></p>
          </div>
        </div>

        <!-- 二维码 -->
        <div class="flex flex-col items-center md:items-end">
          <div class="bg-white p-3 rounded-lg mb-2">
            <!-- 这里可以放置真实的二维码图片 -->
            <!-- <img src="@/assets/images/qrcode.png" alt="微信客服二维码" class="w-20 h-20" /> -->
            <div class="w-20 h-20 bg-gray-200 flex items-center justify-center text-xs text-gray-500 border-2 border-dashed border-gray-400">
              <svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 2V5h1v1H5zM3 13a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1H4a1 1 0 01-1-1v-3zm2 2v-1h1v1H5zM13 4a1 1 0 011-1h3a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1V4zm2 2V5h1v1h-1zM11 4a1 1 0 100-2 1 1 0 000 2zM11 7a1 1 0 100-2 1 1 0 000 2zM11 10a1 1 0 100-2 1 1 0 000 2zM11 13a1 1 0 100-2 1 1 0 000 2zM11 16a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
              </svg>
            </div>
          </div>
          <p class="text-xs text-gray-400">微信客服</p>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="border-t border-gray-600 mt-8 pt-4 text-center">
        <p class="text-xs text-gray-400">
          豫ICP备********号 Copyright © 2025 ######科技有限公司
        </p>
      </div>
    </div>
  </footer>
</template>

<script lang="ts" setup>
// Footer 组件逻辑
</script>

<style scoped>
/* Footer 组件样式 */
</style>
