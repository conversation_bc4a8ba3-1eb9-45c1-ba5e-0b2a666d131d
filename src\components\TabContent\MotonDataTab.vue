<template>
  <div class="w-full">
    <!-- 轮播图和登录表单 -->
    <div class="relative h-[550px] min-h-[550px] overflow-hidden carousel-container">
      <!-- 轮播图 -->
      <Carousel
        autoplay
        :autoplay-speed="4000"
        effect="scrollx"
        arrows
        dots
        class="h-full carousel-wrapper"
      >
        <div class="relative h-full min-h-[550px]">
          <img src="@/assets/images/banner.png" alt="Banner 1" class="w-full h-full min-h-[550px] object-cover" />
        </div>
        <div class="relative h-full min-h-[550px]">
          <img src="@/assets/images/banner1.png" alt="Banner 2" class="w-full h-full min-h-[550px] object-cover" />
        </div>
      
      </Carousel>

      <!-- 登录表单覆盖层 -->
      <LoginOverlay />
    </div>

    <!-- 下方内容区域 -->
    <!-- 第一个区域：永久免费的Temu数据分析工具 -->
    <div class="relative bg-white py-32 overflow-hidden min-h-[600px]">
      <!-- 倒三角黄色背景 -->
      <div class="absolute top-0 right-0 w-1/2 h-full">
        <div class="w-full h-full bg-gradient-to-br from-orange-200 to-orange-400"
             style="clip-path: polygon(30% 0%, 100% 0%, 100% 100%, 0% 100%);"></div>
      </div>

      <div class="max-w-7xl mx-auto px-8 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <!-- 左侧文字内容 -->
          <div class="space-y-8">
            <h2 class="text-5xl font-bold text-orange-600 leading-tight">
              永久免费的Temu数据分析工具
            </h2>
            <p class="text-lg text-gray-700 leading-relaxed">
              基于云端大数据处理技术的智能数据分析，为您提供全面的产品数据洞察及关键指标分析，精准定位市场机会，快速获得竞争优势。
            </p>
            <div class="flex space-x-4">
              <button class="bg-orange-500 hover:bg-orange-600 text-white font-bold px-8 py-4 rounded-lg transition-colors duration-200 text-lg">
                免费使用
              </button>
              <button class="border-2 border-orange-500 text-orange-600 hover:bg-orange-50 font-bold px-8 py-4 rounded-lg transition-colors duration-200 text-lg">
                了解更多
              </button>
            </div>
          </div>

          <!-- 右侧图片区域 -->
          <div class="relative z-20 flex justify-center">
            <img src="@/assets/images/motodata/image001.png" alt="Temu数据分析工具界面"
              class="w-full max-w-lg h-auto rounded-lg shadow-2xl transform hover:scale-105 transition-transform duration-300" />
          </div>
        </div>
      </div>
    </div>

    <!-- 第二、三个区域合并：功能介绍标题 + 四个功能卡片 -->
    <div class="bg-gray-50 py-16">
      <div class="max-w-6xl mx-auto px-8">
        <!-- 功能介绍标题 -->
        <div class="text-center mb-16">
          <h2 class="text-3xl font-bold text-gray-800 mb-4">
            Temu数据分析工具免费为您赋能
          </h2>
          <p class="text-lg text-gray-600">
            作为全网领先的Temu数据工具，我们为您提供精准、实时的数据分析服务
          </p>
        </div>

        <!-- 四个功能卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- 数据选品卡片 -->
          <div class="bg-blue-500 text-white p-8 rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer">
            <div class="text-center">
              <div class="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </div>
              <h3 class="text-xl font-bold mb-3">数据选品</h3>
              <p class="text-sm opacity-90 leading-relaxed">
                多维度数据分析助您精准选品，挖掘潜力爆品，提升选品成功率
              </p>
            </div>
          </div>

          <!-- Temu插件卡片 -->
          <div class="bg-white border-2 border-gray-200 p-8 rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer">
            <div class="text-center">
              <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              </div>
              <h3 class="text-xl font-bold mb-3 text-gray-800">Temu插件</h3>
              <p class="text-sm text-gray-600 leading-relaxed">
                全面对接Temu平台数据接口，实时获取商品信息，数据更新及时
              </p>
            </div>
          </div>

          <!-- 货源寻找卡片 -->
          <div class="bg-purple-500 text-white p-8 rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer">
            <div class="text-center">
              <div class="w-16 h-16 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                </svg>
              </div>
              <h3 class="text-xl font-bold mb-3">货源寻找</h3>
              <p class="text-sm opacity-90 leading-relaxed">
                智能匹配货源，多平台比价，帮您找到最优质的供应商资源
              </p>
            </div>
          </div>

          <!-- 更多功能卡片 -->
          <div class="bg-white border-2 border-gray-200 p-8 rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300 cursor-pointer">
            <div class="text-center">
              <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                </svg>
              </div>
              <h3 class="text-xl font-bold mb-3 text-gray-800">更多功能</h3>
              <p class="text-sm text-gray-600 leading-relaxed">
                数据导出、行业分析、竞品监控等更多实用功能，为您的业务赋能
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第四个区域：专业Temu数据选品 -->
    <div class="bg-white py-16">
      <div class="max-w-7xl mx-auto px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- 左侧文字列表 -->
          <div class="space-y-6">
            <h2 class="text-3xl font-bold text-gray-800 mb-8">
              专业Temu数据选品
            </h2>

            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <p class="text-gray-700">数据精准、功能强大、大数据挖掘、关键词搜索、智能化数据分析助您精准选品</p>
              </div>

              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <p class="text-gray-700">免费提供海量千个商品数据分析报告和详细的数据分析报告</p>
              </div>

              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <p class="text-gray-700">详细的数据分析及数据分析报告，提供精准的数据分析服务</p>
              </div>
            </div>
          </div>

          <!-- 右侧图片 -->
          <div class="relative">
            <img src="@/assets/images/motodata/image4.png" alt="专业Temu数据选品界面"
              class="w-full h-auto rounded-lg shadow-xl" />
          </div>
        </div>
      </div>
    </div>

    <!-- 第五个区域：专业Temu商品数据分析插件 -->
    <div class="bg-white py-16">
      <div class="max-w-7xl mx-auto px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- 左侧图片 -->
          <div class="relative">
            <img src="@/assets/images/motodata/image4.png" alt="专业Temu商品数据分析插件界面"
              class="w-full h-auto rounded-lg shadow-xl" />
          </div>

          <!-- 右侧文字列表 -->
          <div class="space-y-6">
            <h2 class="text-3xl font-bold text-gray-800 mb-8">
              专业Temu商品数据分析插件 - 永久免费
            </h2>

            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-green-500 rounded-sm flex items-center justify-center mt-1 flex-shrink-0">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-gray-700 text-lg">商品销售数、销量趋势、上架时间、以及店铺详细信息等，尽收眼底</p>
              </div>

              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-green-500 rounded-sm flex items-center justify-center mt-1 flex-shrink-0">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-gray-700 text-lg">免费AI评论分析功能，深入了解产品的用户画像，助您精准优化产品策略</p>
              </div>

              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-green-500 rounded-sm flex items-center justify-center mt-1 flex-shrink-0">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-gray-700 text-lg">一键打包下载商品主图、视频、规格图、描述图</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第六个区域：免费货源查找 -->
    <div class="bg-gray-50 py-16">
      <div class="max-w-7xl mx-auto px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- 左侧文字列表 -->
          <div class="space-y-6">
            <h2 class="text-3xl font-bold text-gray-800 mb-8">
              免费货源查找，快速对比多平台货源
            </h2>

            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-green-500 rounded-sm flex items-center justify-center mt-1 flex-shrink-0">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-gray-700 text-lg">以图搜图、图片货源，多平台寻找供应商对比最低价的货源</p>
              </div>

              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-green-500 rounded-sm flex items-center justify-center mt-1 flex-shrink-0">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-gray-700 text-lg">支持1688、义乌购、淘宝、速卖通等多个平台</p>
              </div>

              <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-green-500 rounded-sm flex items-center justify-center mt-1 flex-shrink-0">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <p class="text-gray-700 text-lg">不仅是获取货源，还能对比商品、做竞品分析、价格、销量一手掌握</p>
              </div>
            </div>
          </div>

          <!-- 右侧图片 -->
          <div class="relative">
            <img src="@/assets/images/motodata/image5.png" alt="免费货源查找界面"
              class="w-full h-auto rounded-lg shadow-xl" />
          </div>
        </div>
      </div>
    </div>

    <!-- 注册按钮区域 -->
    <div class="bg-white py-16">
      <div class="max-w-4xl mx-auto text-center px-8">
        <h3 class="text-2xl font-medium text-gray-600 mb-8">
          立即注册，开启您的0门槛跨境电商之旅！
        </h3>
        <button
          @click="scrollToTopAndRegister"
          class="w-full max-w-2xl mx-auto bg-white border-2 border-blue-400 text-blue-600 font-medium py-4 px-8 rounded-lg hover:bg-blue-50 transition-colors duration-200 text-lg"
        >
          免费注册会员
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick } from 'vue';
import { Carousel } from 'ant-design-vue';
import LoginOverlay from '@/components/LoginOverlay/index.vue';
import { useLoginState, LoginStateEnum } from '@/views/sys/login/useLogin';

const { setLoginState } = useLoginState();

// 滚动到顶部并切换到注册状态
function scrollToTopAndRegister() {
  console.log('scrollToTopAndRegister 被调用');

  // 先切换到注册状态
  setLoginState(LoginStateEnum.REGISTER);
  console.log('切换到注册状态');

  // 使用 nextTick 确保DOM更新后再执行滚动
  nextTick(() => {
    console.log('nextTick 执行滚动');

    // 设置 document.body.scrollTop
    document.body.scrollTop = 0;

    // 设置 document.documentElement.scrollTop
    document.documentElement.scrollTop = 0;

    // 使用 window.scrollTo 平滑滚动
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    console.log('子组件滚动执行完成');
  });
}
</script>

<style scoped>
/* 确保轮播图容器高度 */
.carousel-container {
  height: 550px !important;
  min-height: 550px !important;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.carousel-wrapper :deep(.ant-carousel) {
  height: 100% !important;
  min-height: 550px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-list) {
  height: 100% !important;
  min-height: 550px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-track) {
  height: 100% !important;
  min-height: 550px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-slide) {
  height: 100% !important;
  min-height: 550px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-slide > div) {
  height: 100% !important;
  min-height: 550px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-slide img) {
  height: 550px !important;
  min-height: 550px !important;
  width: 100% !important;
  object-fit: cover !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-dots) {
  bottom: 20px;
}

.carousel-wrapper :deep(.ant-carousel .slick-dots li button) {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
}

.carousel-wrapper :deep(.ant-carousel .slick-dots li.slick-active button) {
  background: white;
}

/* 轮播图箭头样式 */
.carousel-wrapper :deep(.ant-carousel .slick-arrow) {
  display: block !important;
  z-index: 10;
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  border: none;
  color: white;
  font-size: 18px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-wrapper :deep(.ant-carousel .slick-arrow:hover) {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.carousel-wrapper :deep(.ant-carousel .slick-prev) {
  left: 20px;
}

.carousel-wrapper :deep(.ant-carousel .slick-next) {
  right: 20px;
}

.carousel-wrapper :deep(.ant-carousel .slick-arrow::before) {
  display: none;
}

.carousel-wrapper :deep(.ant-carousel .slick-prev::after) {
  content: '‹';
  font-size: 24px;
  font-weight: bold;
}

.carousel-wrapper :deep(.ant-carousel .slick-next::after) {
  content: '›';
  font-size: 24px;
  font-weight: bold;
}
</style>
