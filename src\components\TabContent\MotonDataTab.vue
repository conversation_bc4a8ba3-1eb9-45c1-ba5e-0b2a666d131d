<template>
  <div class="w-full">
    <!-- 轮播图和登录表单 -->
    <div class="relative h-[600px] min-h-[600px] overflow-hidden carousel-container">
      <!-- 轮播图 -->
      <Carousel
        autoplay
        :autoplay-speed="4000"
        effect="scrollx"
        arrows
        dots
        class="h-full carousel-wrapper"
      >
        <div class="relative h-full min-h-[600px]">
          <img src="@/assets/images/banner.png" alt="Banner 1" class="w-full h-full min-h-[600px] object-cover" />
        </div>
        <div class="relative h-full min-h-[600px]">
          <img src="@/assets/images/banner1.png" alt="Banner 2" class="w-full h-full min-h-[600px] object-cover" />
        </div>
      
      </Carousel>

      <!-- 登录表单覆盖层 -->
      <LoginOverlay />
    </div>

    <!-- 下方内容区域 -->
    <div class="bg-white">
      <!-- Title -->
      <div class="text-center py-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">
          全网最全面最精准TEMU数据链接直达商品详情
        </h2>
      </div>

      <!-- 大图 -->
      <div class="px-8 pb-12">
        <div class="max-w-4xl mx-auto">
          <img src="@/assets/images/motodata/image.png" alt="全网最全面最精准TEMU数据链接直达商品详情"
            class="w-full h-auto rounded-lg shadow-lg" style="max-height: none; height: auto;" />
        </div>
      </div>
    </div>

    <div class="bg-white">
      <!-- Title -->
      <div class="text-center py-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">
          商品ID、标题搜索全网数据精准细分类目（中英显示）
        </h2>
      </div>

      <!-- 大图 -->
      <div class="px-8 pb-12">
        <div class="max-w-4xl mx-auto">
          <img src="@/assets/images/motodata/image2.png" alt="商品ID、标题搜索全网数据精准细分类目（中英显示）"
            class="w-full h-auto rounded-lg shadow-lg" style="max-height: none; height: auto;" />
        </div>
      </div>
    </div>

    <div class="bg-white">
      <!-- Title -->
      <div class="text-center py-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">
          全面高级筛选爆品一览无遗
        </h2>
      </div>

      <!-- 大图 -->
      <div class="px-8 pb-12">
        <div class="max-w-4xl mx-auto">
          <img src="@/assets/images/motodata/image3.png" alt="全面高级筛选爆品一览无遗"
            class="w-full h-auto rounded-lg shadow-lg" />
        </div>
      </div>
    </div>

    <div class="bg-white">
      <!-- Title -->
      <div class="text-center py-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">
          产品数据趋势全面分析
        </h2>
      </div>

      <!-- 大图 -->
      <div class="px-8 pb-12">
        <div class="max-w-4xl mx-auto">
          <img src="@/assets/images/motodata/image4.png" alt="产品数据趋势全面分析"
            class="w-full h-auto rounded-lg shadow-lg" />
        </div>
      </div>
    </div>

    <div class="bg-white">
      <!-- Title -->
      <div class="text-center py-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">
          一键智能找货快速匹配1688/拼多多货源
        </h2>
      </div>

      <!-- 大图 -->
      <div class="px-8 pb-12">
        <div class="max-w-4xl mx-auto">
          <img src="@/assets/images/motodata/image5.png" alt="一键智能找货快速匹配1688/拼多多货源"
            class="w-full h-auto rounded-lg shadow-lg" />
        </div>
      </div>
    </div>

    <div class="bg-white">
      <!-- Title -->
      <div class="text-center py-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">
          TEMU前端链接分析插件直观分析一目了然
        </h2>
      </div>

      <!-- 大图 -->
      <div class="px-8 pb-12">
        <div class="max-w-4xl mx-auto">
          <img src="@/assets/images/motodata/image6.png" alt="TEMU前端链接分析插件直观分析一目了然"
            class="w-full h-auto rounded-lg shadow-lg" />
        </div>
      </div>
    </div>

    <div class="bg-white">
      <!-- Title -->
      <div class="text-center py-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">
          一键自动上架，多店铺切换铺货
        </h2>
      </div>

      <!-- 大图 -->
      <div class="px-8 pb-12">
        <div class="max-w-4xl mx-auto">
          <img src="@/assets/images/motodata/image7.png" alt="一键自动上架，多店铺切换铺货"
            class="w-full h-auto rounded-lg shadow-lg" />
        </div>
      </div>
    </div>

    <div class="bg-white">
      <!-- Title -->
      <div class="text-center py-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">
          临时屏蔽通知，不再焦急等待
        </h2>
      </div>

      <!-- 大图 -->
      <div class="px-8 pb-12">
        <div class="max-w-4xl mx-auto">
          <img src="@/assets/images/motodata/image8.png" alt="临时屏蔽通知，不再焦急等待"
            class="w-full h-auto rounded-lg shadow-lg" />
        </div>
      </div>
    </div>

    <div class="bg-white">
      <!-- Title -->
      <div class="text-center py-12">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">
          自动加入发货台，快人一步
        </h2>
      </div>

      <!-- 大图 -->
      <div class="px-8 pb-12">
        <div class="max-w-4xl mx-auto">
          <img src="@/assets/images/motodata/image9.png" alt="自动加入发货台，快人一步"
            class="w-full h-auto rounded-lg shadow-lg" />
        </div>
      </div>

      <!-- 注册按钮区域 -->
      <div class="px-8 pb-16">
        <div class="max-w-4xl mx-auto text-center">
          <h3 class="text-2xl font-medium text-gray-600 mb-8">
            立即注册，开启您的0门槛跨境电商之旅！
          </h3>
          <button 
            @click="scrollToTopAndRegister"
            class="w-full max-w-2xl mx-auto bg-white border-2 border-blue-400 text-blue-600 font-medium py-4 px-8 rounded-lg hover:bg-blue-50 transition-colors duration-200 text-lg"
          >
            免费注册会员
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick } from 'vue';
import { Carousel } from 'ant-design-vue';
import LoginOverlay from '@/components/LoginOverlay/index.vue';
import { useLoginState, LoginStateEnum } from '@/views/sys/login/useLogin';

const { setLoginState } = useLoginState();

// 滚动到顶部并切换到注册状态
function scrollToTopAndRegister() {
  console.log('scrollToTopAndRegister 被调用');

  // 先切换到注册状态
  setLoginState(LoginStateEnum.REGISTER);
  console.log('切换到注册状态');

  // 使用 nextTick 确保DOM更新后再执行滚动
  nextTick(() => {
    console.log('nextTick 执行滚动');

    // 设置 document.body.scrollTop
    document.body.scrollTop = 0;

    // 设置 document.documentElement.scrollTop
    document.documentElement.scrollTop = 0;

    // 使用 window.scrollTo 平滑滚动
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });

    console.log('子组件滚动执行完成');
  });
}
</script>

<style scoped>
/* 确保轮播图容器高度 */
.carousel-container {
  height: 600px !important;
  min-height: 600px !important;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.carousel-wrapper :deep(.ant-carousel) {
  height: 100% !important;
  min-height: 600px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-list) {
  height: 100% !important;
  min-height: 600px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-track) {
  height: 100% !important;
  min-height: 600px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-slide) {
  height: 100% !important;
  min-height: 600px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-slide > div) {
  height: 100% !important;
  min-height: 600px !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-slide img) {
  height: 600px !important;
  min-height: 600px !important;
  width: 100% !important;
  object-fit: cover !important;
}

.carousel-wrapper :deep(.ant-carousel .slick-dots) {
  bottom: 20px;
}

.carousel-wrapper :deep(.ant-carousel .slick-dots li button) {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
}

.carousel-wrapper :deep(.ant-carousel .slick-dots li.slick-active button) {
  background: white;
}

/* 轮播图箭头样式 */
.carousel-wrapper :deep(.ant-carousel .slick-arrow) {
  display: block !important;
  z-index: 10;
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  border: none;
  color: white;
  font-size: 18px;
  line-height: 50px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-wrapper :deep(.ant-carousel .slick-arrow:hover) {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.carousel-wrapper :deep(.ant-carousel .slick-prev) {
  left: 20px;
}

.carousel-wrapper :deep(.ant-carousel .slick-next) {
  right: 20px;
}

.carousel-wrapper :deep(.ant-carousel .slick-arrow::before) {
  display: none;
}

.carousel-wrapper :deep(.ant-carousel .slick-prev::after) {
  content: '‹';
  font-size: 24px;
  font-weight: bold;
}

.carousel-wrapper :deep(.ant-carousel .slick-next::after) {
  content: '›';
  font-size: 24px;
  font-weight: bold;
}
</style>
