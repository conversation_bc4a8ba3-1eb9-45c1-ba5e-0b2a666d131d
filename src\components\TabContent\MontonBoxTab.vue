<template>
  <div class="w-full min-h-[650px] relative">
    <!-- <PERSON>on Box背景 -->
    <div class="absolute inset-0 bg-gradient-to-br from-purple-600 to-purple-800"></div>

    <!-- 登录表单覆盖层 -->
    <LoginOverlay />

    <!-- Monton Box内容 -->
    <div class="relative flex items-center justify-center z-20 min-h-[650px]">
      <div class="max-w-4xl mx-auto px-8 text-center text-white">
        <h1 class="text-5xl font-bold mb-6">Monton Box跨境网盒</h1>
        <p class="text-xl mb-8 text-purple-100">
          一站式跨境电商工具箱，助力您的全球业务
        </p>
        
        <!-- 功能模块 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-12">
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold mb-2">数据分析</h3>
            <p class="text-purple-100 text-sm">实时监控销售数据</p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold mb-2">选品工具</h3>
            <p class="text-purple-100 text-sm">智能选品推荐</p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold mb-2">库存管理</h3>
            <p class="text-purple-100 text-sm">多平台库存同步</p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
            <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold mb-2">营销推广</h3>
            <p class="text-purple-100 text-sm">自动化营销工具</p>
          </div>
        </div>

        <!-- 特色功能 -->
        <div class="mt-16">
          <h2 class="text-3xl font-bold mb-8">核心功能</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="text-left bg-white/5 rounded-lg p-6">
              <h3 class="text-xl font-semibold mb-4">🚀 一键上架</h3>
              <p class="text-purple-100">支持多平台一键上架，节省90%的时间成本</p>
            </div>
            <div class="text-left bg-white/5 rounded-lg p-6">
              <h3 class="text-xl font-semibold mb-4">📊 智能定价</h3>
              <p class="text-purple-100">AI智能定价策略，最大化利润空间</p>
            </div>
            <div class="text-left bg-white/5 rounded-lg p-6">
              <h3 class="text-xl font-semibold mb-4">🔄 自动同步</h3>
              <p class="text-purple-100">订单、库存、物流信息实时同步</p>
            </div>
            <div class="text-left bg-white/5 rounded-lg p-6">
              <h3 class="text-xl font-semibold mb-4">📈 数据洞察</h3>
              <p class="text-purple-100">深度数据分析，助力业务决策</p>
            </div>
          </div>
        </div>

        <!-- 行动按钮 -->
        <div class="mt-12">
          <button class="bg-white text-purple-600 font-semibold py-3 px-8 rounded-lg hover:bg-purple-50 transition-colors duration-200 mr-4">
            免费试用
          </button>
          <button class="border-2 border-white text-white font-semibold py-3 px-8 rounded-lg hover:bg-white hover:text-purple-600 transition-colors duration-200">
            了解更多
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LoginOverlay from '@/components/LoginOverlay/index.vue';
// Monton Box组件逻辑
</script>

<style scoped>
/* Monton Box组件样式 */
</style>
