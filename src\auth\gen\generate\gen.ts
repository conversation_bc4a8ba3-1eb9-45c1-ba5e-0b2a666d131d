/** 代码生成权限标识 */
export enum GenAuth {
  /** 代码生成 | 代码生成管理 | 列表 */
  LIST = 'FE:gen:generate:gen:list',
  /** 代码生成 | 代码生成管理 | 修改 */
  EDIT = 'FE:gen:generate:gen:edit',
  /** 代码生成 | 代码生成管理 | 删除 */
  DEL = 'FE:gen:generate:gen:del',
  /** 代码生成 | 代码生成管理 | 导入 */
  IMPORT = 'FE:gen:generate:gen:import',
  /** 代码生成 | 代码生成管理 | 生成 */
  PREVIEW = 'FE:gen:generate:gen:preview',
  /** 代码生成 | 代码生成管理 | 下载 */
  CODE = 'FE:gen:generate:gen:code',
}
