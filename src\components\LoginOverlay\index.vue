<template>
  <div class="absolute inset-0 flex items-center justify-end pr-16 py-8 z-50">
    <div class="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl pr-8 pl-8 w-[460px] my-8">
      <transition name="fade" mode="out-in" appear>
        <div v-if="getLoginState === LoginStateEnum.LOGIN" key="login">
          <LoginForm />
        </div>
        <div v-else-if="getLoginState === LoginStateEnum.REGISTER" key="register">
          <RegisterForm />
        </div>
        <div v-else-if="getLoginState === LoginStateEnum.RESET_PASSWORD" key="reset">
          <ForgetPasswordForm />
        </div>
        <div v-else-if="getLoginState === LoginStateEnum.MOBILE" key="mobile">
          <MobileForm />
        </div>
        <div v-else-if="getLoginState === LoginStateEnum.QR_CODE" key="qrcode">
          <QrCodeForm />
        </div>
      </transition>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LoginForm from '@/views/sys/login/LoginForm.vue';
import RegisterForm from '@/views/sys/login/RegisterForm.vue';
import ForgetPasswordForm from '@/views/sys/login/ForgetPasswordForm.vue';
import MobileForm from '@/views/sys/login/MobileForm.vue';
import QrCodeForm from '@/views/sys/login/QrCodeForm.vue';
import { useLoginState, LoginStateEnum } from '@/views/sys/login/useLogin';

const { getLoginState } = useLoginState();
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
