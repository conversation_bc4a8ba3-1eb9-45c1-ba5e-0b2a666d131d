import { VxeGlobalRendererOptions } from 'vxe-table';
import {
  createEditRender,
  createDefault<PERSON><PERSON>,
  createF<PERSON>er<PERSON><PERSON>,
  createDefaultFilterRender,
  createFormItemRender,
  createToolbarToolRender,
} from './common';

export default {
  renderTableDefault: createDefaultRender(),
  renderTableEdit: createEditRender(),
  renderTableFilter: createFilterR<PERSON>(),
  tableFilterDefaultMethod: createDefaultFilterRender(),
  renderFormItemContent: createFormItemRender(),
  renderToolbarTool: createToolbarToolRender(),
} as VxeGlobalRendererOptions;
