<template>
  <div class="w-full min-h-[650px] relative">
    <!-- 公告资讯背景 -->
    <div class="absolute inset-0 bg-gradient-to-br from-indigo-600 to-blue-800"></div>

    <!-- 登录表单覆盖层 -->
    <LoginOverlay />

    <!-- 公告资讯内容 -->
    <div class="relative z-20 min-h-[650px] py-16">
      <div class="max-w-6xl mx-auto px-8">
        <div class="text-center text-white mb-12">
          <h1 class="text-5xl font-bold mb-6">公告资讯</h1>
          <p class="text-xl text-indigo-100">
            获取最新的行业动态、产品更新和重要公告
          </p>
        </div>

        <!-- 重要公告 -->
        <div class="mb-12">
          <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
            <svg class="w-6 h-6 mr-2 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            重要公告
          </h2>
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 border-l-4 border-yellow-400">
            <h3 class="text-xl font-semibold text-white mb-2">系统维护通知</h3>
            <p class="text-indigo-100 mb-2">
              为了提供更好的服务体验，我们将于2025年1月15日 02:00-06:00 进行系统维护升级。
            </p>
            <span class="text-sm text-indigo-200">发布时间：2025-01-10</span>
          </div>
        </div>

        <!-- 最新资讯 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/15 transition-colors duration-200">
            <div class="flex items-center mb-3">
              <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded">产品更新</span>
              <span class="text-indigo-200 text-sm ml-auto">2025-01-08</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">
              Moton Data 3.0 版本正式发布
            </h3>
            <p class="text-indigo-100 text-sm">
              新增AI智能选品功能，优化数据分析算法，提升用户体验...
            </p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/15 transition-colors duration-200">
            <div class="flex items-center mb-3">
              <span class="bg-green-500 text-white text-xs px-2 py-1 rounded">行业动态</span>
              <span class="text-indigo-200 text-sm ml-auto">2025-01-05</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">
              2025年跨境电商趋势报告
            </h3>
            <p class="text-indigo-100 text-sm">
              深度解析2025年跨境电商市场趋势，助力商家把握商机...
            </p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/15 transition-colors duration-200">
            <div class="flex items-center mb-3">
              <span class="bg-purple-500 text-white text-xs px-2 py-1 rounded">功能介绍</span>
              <span class="text-indigo-200 text-sm ml-auto">2025-01-03</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">
              如何使用智能定价功能
            </h3>
            <p class="text-indigo-100 text-sm">
              详细介绍智能定价功能的使用方法和最佳实践...
            </p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/15 transition-colors duration-200">
            <div class="flex items-center mb-3">
              <span class="bg-red-500 text-white text-xs px-2 py-1 rounded">政策解读</span>
              <span class="text-indigo-200 text-sm ml-auto">2024-12-28</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">
              最新跨境电商政策解读
            </h3>
            <p class="text-indigo-100 text-sm">
              解读最新的跨境电商相关政策，帮助商家合规经营...
            </p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/15 transition-colors duration-200">
            <div class="flex items-center mb-3">
              <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded">活动通知</span>
              <span class="text-indigo-200 text-sm ml-auto">2024-12-25</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">
              新年优惠活动开启
            </h3>
            <p class="text-indigo-100 text-sm">
              新年特惠活动正式开启，多重优惠等您来享...
            </p>
          </div>

          <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/15 transition-colors duration-200">
            <div class="flex items-center mb-3">
              <span class="bg-indigo-500 text-white text-xs px-2 py-1 rounded">技术分享</span>
              <span class="text-indigo-200 text-sm ml-auto">2024-12-20</span>
            </div>
            <h3 class="text-lg font-semibold text-white mb-2">
              API接口使用指南
            </h3>
            <p class="text-indigo-100 text-sm">
              详细的API接口使用指南，帮助开发者快速集成...
            </p>
          </div>
        </div>

        <!-- 订阅通知 -->
        <div class="bg-white/5 rounded-2xl p-8 text-center">
          <h2 class="text-2xl font-bold text-white mb-4">订阅最新资讯</h2>
          <p class="text-indigo-100 mb-6">
            订阅我们的资讯推送，第一时间获取最新动态和重要公告
          </p>
          <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input 
              type="email" 
              placeholder="请输入您的邮箱地址"
              class="flex-1 px-4 py-2 rounded-lg bg-white/10 border border-white/20 text-white placeholder-indigo-200 focus:outline-none focus:border-white/40"
            />
            <button class="bg-white text-indigo-600 font-semibold py-2 px-6 rounded-lg hover:bg-indigo-50 transition-colors duration-200">
              订阅
            </button>
          </div>
        </div>

        <!-- 联系我们 -->
        <div class="mt-12 text-center">
          <p class="text-indigo-100 mb-4">
            如有疑问或建议，欢迎联系我们
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button class="bg-white text-indigo-600 font-semibold py-2 px-6 rounded-lg hover:bg-indigo-50 transition-colors duration-200">
              在线客服
            </button>
            <button class="border-2 border-white text-white font-semibold py-2 px-6 rounded-lg hover:bg-white hover:text-indigo-600 transition-colors duration-200">
              意见反馈
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LoginOverlay from '@/components/LoginOverlay/index.vue';
// 公告资讯组件逻辑
</script>

<style scoped>
/* 公告资讯组件样式 */
</style>
