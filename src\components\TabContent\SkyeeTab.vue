<template>
  <div class="skyee-page">
    <!-- 顶部蓝色渐变背景区域 -->
    <div class="hero-section">
      <!-- 登录表单覆盖层 -->
      <LoginOverlay />

      <div class="hero-content">
        <h1 class="hero-title">更好用的全球支付解决</h1>
        <p class="hero-subtitle">专业跨境、便捷收款的全球化支付平台</p>
        <!-- <div class="hero-buttons">
          <button class="btn-primary">立即开通</button>
          <button class="btn-secondary">了解更多</button>
        </div> -->
      </div>
      <!-- 图片区域预留 -->
      <div class="hero-image">
        <!-- 图片区域暂时空着 -->
      </div>
    </div>

    <!-- 全球覆盖，一站管理 -->
    <div class="global-section">
      <h2 class="section-title">全球覆盖，一站管理</h2>
      <div class="global-content">
        <!-- 摩通时代 中心图标和周围的功能点 -->
        <div class="skyee-center">
          <div class="feature-point point-1">收款便捷</div>
          <div class="feature-point point-2">多币种支持</div>
          <div class="feature-point point-3">费率透明</div>
          <div class="feature-point point-4">风控安全</div>
          <div class="feature-point point-5">客服支持</div>
          <div class="feature-point point-6">多渠道接入</div>
          <div class="skyee-logo">摩通时代</div>
        </div>
      </div>
    </div>

    <!-- 我选择，我信赖 -->
    <div class="advantages-section">
      <div class="advantages-header">
        <h2 class="section-title">我选择，我信赖</h2>
        <div class="title-underline"></div>
      </div>
      <div class="advantages-container">
        <!-- 左列 -->
        <div class="advantages-column">
          <div class="advantage-item">
            <div class="advantage-icon">
              <div class="icon-circle"></div>
            </div>
            <div class="advantage-content">
              <h3>更快更省</h3>
              <p>用金融科技的力量改变传统，降低成本会计核算，让对接和支付更简单，无需担心汇率风险</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">
              <div class="icon-circle"></div>
            </div>
            <div class="advantage-content">
              <h3>品牌保障</h3>
              <p>专注跨境支付多年，24*7用户的跨境支付体验，持续优化对接流程与专业服务</p>
            </div>
          </div>
        </div>

        <!-- 右列 -->
        <div class="advantages-column">
          <div class="advantage-item">
            <div class="advantage-icon">
              <div class="icon-circle"></div>
            </div>
            <div class="advantage-content">
              <h3>解决方案</h3>
              <p>多样化支付方案，开发、运营、下沉到跨境电商，成为一站服务</p>
            </div>
          </div>
          <div class="advantage-item">
            <div class="advantage-icon">
              <div class="icon-circle"></div>
            </div>
            <div class="advantage-content">
              <h3>合规先行</h3>
              <p>接受国际权威监管，获得合规，安全合规ISO主证书：17.12.02270，英国MSB注册号：31000250900517</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行业标杆为实力为跨境电商赋能服务 -->
    <div class="industry-section">
      <h2 class="section-title">行业标杆为实力为跨境电商赋能服务</h2>
      <div class="industry-content">
        <!-- 左侧图片区域 -->
        <div class="industry-image">
          <!-- 图片区域暂时空着 -->
        </div>
        <!-- 右侧内容 -->
        <div class="industry-text">
          <div class="industry-logo">miwi</div>
          <p>作为跨境支付行业的领军企业，我们致力于为全球跨境电商提供专业、安全、便捷的支付解决方案。</p>
          <button class="learn-more-btn">了解更多</button>
        </div>
      </div>
    </div>

    <!-- 合作伙伴 -->
    <div class="partners-section">
      <div class="partners-logos">
        <!-- 合作伙伴logo区域，暂时空着 -->
        <div class="partner-logo"></div>
        <div class="partner-logo"></div>
        <div class="partner-logo"></div>
        <div class="partner-logo"></div>
        <div class="partner-logo"></div>
      </div>
    </div>

    <!-- 新闻资讯 -->
    <div class="news-section">
      <h2 class="section-title">新闻资讯</h2>
      <div class="news-grid">
        <div class="news-item">
          <div class="news-image">
            <!-- 新闻图片区域 -->
          </div>
          <div class="news-content">
            <h3>Skyee跨境支付解决方案，助力中国企业"走出去"</h3>
            <p class="news-date">05-07</p>
          </div>
        </div>
        <div class="news-item">
          <div class="news-content">
            <h3>Skyee获得跨境支付业务许可证，合规经营再上新台阶</h3>
            <p class="news-date">01-13</p>
          </div>
        </div>
        <div class="news-item">
          <div class="news-content">
            <h3>IPA</h3>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LoginOverlay from '@/components/LoginOverlay/index.vue';
// skyee收款组件逻辑
</script>

<style scoped>
.skyee-page {
  width: 100%;
  min-height: 100vh;
}

/* 顶部蓝色渐变背景区域 */
.hero-section {
  position: relative;
  min-height: 650px;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: hidden;
  padding-left: 5%;
}

.hero-content {
  position: relative;
  z-index: 5;
  text-align: left;
  color: white;
  max-width: 600px;
  padding: 0 2rem;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-primary {
  background: #fbbf24;
  color: #1f2937;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: #f59e0b;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  padding: 0.75rem 2rem;
  border: 2px solid white;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: white;
  color: #1e40af;
}

.hero-image {
  position: absolute;
  right: 10%;
  top: 50%;
  transform: translateY(-50%);
  width: 300px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

/* 全球覆盖，一站管理 */
.global-section {
  padding: 4rem 2rem;
  background: #f8fafc;
  text-align: center;
}

.section-title {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 3rem;
}

.global-content {
  max-width: 800px;
  margin: 0 auto;
}

.skyee-center {
  position: relative;
  width: 500px;
  height: 500px;
  margin: 0 auto;
}

.skyee-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2.5rem;
  font-weight: bold;
  color: #3b82f6;
  background: white;
  padding: 1.5rem 2.5rem;
  border-radius: 50%;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 3px solid #3b82f6;
  z-index: 5;
}

.feature-point {
  position: absolute;
  background: #3b82f6;
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: 2rem;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-point:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* 圆形环绕布局 - 6个点均匀分布在圆周上 */
.feature-point.point-1 {
  /* 12点钟方向 (0度) */
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
}

.feature-point.point-2 {
  /* 2点钟方向 (60度) */
  top: 80px;
  right: 60px;
  transform: rotate(0deg);
}

.feature-point.point-3 {
  /* 4点钟方向 (120度) */
  bottom: 80px;
  right: 60px;
  transform: rotate(0deg);
}

.feature-point.point-4 {
  /* 6点钟方向 (180度) */
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
}

.feature-point.point-5 {
  /* 8点钟方向 (240度) */
  bottom: 80px;
  left: 60px;
  transform: rotate(0deg);
}

.feature-point.point-6 {
  /* 10点钟方向 (300度) */
  top: 80px;
  left: 60px;
  transform: rotate(0deg);
}

/* 我选择，我信赖 */
.advantages-section {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.advantages-header {
  text-align: center;
  margin-bottom: 3rem;
}

.title-underline {
  width: 60px;
  height: 3px;
  background: #3b82f6;
  margin: 1rem auto 0;
  border-radius: 2px;
}

.advantages-container {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  gap: 4rem;
}

.advantages-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.advantage-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  text-align: left;
}

.advantage-icon {
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.icon-circle {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
}

.advantage-content h3 {
  color: #3b82f6;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.advantage-content p {
  color: #64748b;
  line-height: 1.6;
  font-size: 0.9rem;
}

/* 行业标杆为实力为跨境电商赋能服务 */
.industry-section {
  padding: 4rem 2rem;
  background: #f8fafc;
}

.industry-content {
  display: flex;
  align-items: center;
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
}

.industry-image {
  flex: 1;
  height: 300px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 1rem;
  border: 2px dashed rgba(59, 130, 246, 0.3);
}

.industry-text {
  flex: 1;
  text-align: left;
}

.industry-logo {
  font-size: 2rem;
  font-weight: bold;
  color: #3b82f6;
  margin-bottom: 1rem;
}

.industry-text p {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.learn-more-btn {
  background: #3b82f6;
  color: white;
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.learn-more-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
}

/* 合作伙伴 */
.partners-section {
  padding: 2rem;
  background: white;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
}

.partners-logos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  max-width: 1000px;
  margin: 0 auto;
  flex-wrap: wrap;
}

.partner-logo {
  width: 120px;
  height: 60px;
  background: #f3f4f6;
  border-radius: 0.5rem;
  border: 1px dashed #d1d5db;
}

/* 新闻资讯 */
.news-section {
  padding: 4rem 2rem;
  background: white;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.news-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.news-item:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.news-image {
  width: 80px;
  height: 60px;
  background: #f3f4f6;
  border-radius: 0.5rem;
  border: 1px dashed #d1d5db;
  flex-shrink: 0;
}

.news-content {
  flex: 1;
}

.news-content h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.news-date {
  font-size: 0.875rem;
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .skyee-center {
    width: 350px;
    height: 350px;
  }

  .skyee-logo {
    font-size: 1.8rem;
    padding: 1rem 1.5rem;
  }

  .feature-point {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }

  /* 移动端调整圆形布局 */
  .feature-point.point-1 {
    top: 20px;
  }

  .feature-point.point-2 {
    top: 60px;
    right: 40px;
  }

  .feature-point.point-3 {
    bottom: 60px;
    right: 40px;
  }

  .feature-point.point-4 {
    bottom: 20px;
  }

  .feature-point.point-5 {
    bottom: 60px;
    left: 40px;
  }

  .feature-point.point-6 {
    top: 60px;
    left: 40px;
  }

  .industry-content {
    flex-direction: column;
  }

  .advantages-container {
    flex-direction: column;
    gap: 2rem;
  }

  .advantages-column {
    gap: 1.5rem;
  }

  .partners-logos {
    gap: 1.5rem;
  }

  .news-item {
    flex-direction: column;
  }

  .news-image {
    width: 100%;
    height: 120px;
  }
}
</style>
