<template>
  <div :class="prefixCls" class="relative w-full h-full">
    <!-- 顶部导航栏 - 通栏样式 -->
    <div class="absolute top-0 left-0 right-0 z-40 bg-gradient-to-r from-gray-800 to-gray-700 shadow-lg">
      <div class="flex items-center justify-between px-6 py-4">
        <!-- Logo -->
        <div class="flex items-center">
          <AppLogo class="text-white login-logo" />
          <!-- <span class="text-white text-xl font-semibold ml-2">知了数据</span> -->
        </div>

        <!-- 右侧导航 -->
        <div class="flex items-center space-x-8 text-white">
          <a @click.prevent="setActiveTab('home')" :class="[
            'font-medium transition-colors duration-200 cursor-pointer',
            activeTab === 'home' ? 'text-white border-b-2 border-white pb-1' : 'text-white/80 hover:text-white'
          ]">
            Moton Data
          </a>
          <a @click.prevent="setActiveTab('clients')" :class="[
            'font-medium transition-colors duration-200 cursor-pointer',
            activeTab === 'clients' ? 'text-white border-b-2 border-white pb-1' : 'text-white/80 hover:text-white'
          ]">
            skyee收款
          </a>
          <a @click.prevent="setActiveTab('online')" :class="[
            'font-medium transition-colors duration-200 cursor-pointer',
            activeTab === 'online' ? 'text-white border-b-2 border-white pb-1' : 'text-white/80 hover:text-white'
          ]">
            Monton Box跨境网盒
          </a>
          <a @click.prevent="setActiveTab('products')" :class="[
            'font-medium transition-colors duration-200 cursor-pointer',
            activeTab === 'products' ? 'text-white border-b-2 border-white pb-1' : 'text-white/80 hover:text-white'
          ]">
            生态
          </a>
          <a @click.prevent="setActiveTab('news')" :class="[
            'font-medium transition-colors duration-200 cursor-pointer',
            activeTab === 'news' ? 'text-white border-b-2 border-white pb-1' : 'text-white/80 hover:text-white'
          ]">
            公告资讯
          </a>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 - 根据tab切换整个区域 -->
    <div class="relative mt-16">
      <transition name="fade" mode="out-in">
        <!-- 首页内容 -->
        <div v-if="activeTab === 'home'" key="home" class="w-full">
          <MotonDataTab />



          <!-- Footer 组件 -->
          <Footer />
        </div>

        <!-- skyee收款页面 -->
        <div v-else-if="activeTab === 'clients'" key="clients" class="w-full min-h-[650px]">
          <SkyeeTab />
        </div>

        <!-- Monton Box跨境网盒页面 -->
        <div v-else-if="activeTab === 'online'" key="online" class="w-full min-h-[650px] relative">
          <MontonBoxTab />
        </div>

        <!-- 生态页面 -->
        <div v-else-if="activeTab === 'products'" key="products" class="w-full min-h-[650px] relative">
          <EcosystemTab />
        </div>

        <!-- 公告资讯页面 -->
        <div v-else-if="activeTab === 'news'" key="news" class="w-full min-h-[650px] relative">
          <NewsTab />
        </div>
      </transition>
    </div>

    <!-- 移动端Logo -->
    <span class="-enter-x xl:hidden absolute top-4 left-4 z-30">
      <AppLogo :alwaysShowTitle="true" class="text-white" />
    </span>

    <!-- 返回顶部按钮 -->
    <!-- <a-back-top
      :visibilityHeight="200"
      
    >
      <div class="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg transition-colors duration-200">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
        </svg>
      </div>
    </a-back-top> -->
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { AppLogo } from '@/components/Application';
import Footer from '@/components/Footer/index.vue';
import MotonDataTab from '@/components/TabContent/MotonDataTab.vue';
import SkyeeTab from '@/components/TabContent/SkyeeTab.vue';
import MontonBoxTab from '@/components/TabContent/MontonBoxTab.vue';
import EcosystemTab from '@/components/TabContent/EcosystemTab.vue';
import NewsTab from '@/components/TabContent/NewsTab.vue';
import { useDesign } from '@/hooks/web/useDesign';

defineProps({
  sessionTimeout: {
    type: Boolean,
  },
});

const { prefixCls } = useDesign('login');

// Tab切换状态
const activeTab = ref('home');

// 切换tab的方法
function setActiveTab(tab: string) {
  activeTab.value = tab;
}




</script>

<style lang="less">
@prefix-cls: ~'@{namespace}-login';
@logo-prefix-cls: ~'@{namespace}-app-logo';
@countdown-prefix-cls: ~'@{namespace}-countdown-input';

.@{prefix-cls} {
  min-height: 100vh;
  overflow: auto;

  // 表单区域样式
  &-form {
    background-color: transparent;

    // 减少表单项间距
    .ant-form-item {
      margin-bottom: 16px !important;
    }

    // 表单标题间距优化
    .ant-typography {
      margin-bottom: 20px !important;
    }

    // 按钮组间距优化
    .ant-btn+.ant-btn {
      margin-left: 8px !important;
    }

    // 底部按钮组间距
    .ant-form-item:last-child {
      margin-bottom: 0 !important;
    }
  }

  // 移动端适配
  @media (max-width: @screen-xl) {

    // 移动端时调整表单位置
    .absolute.top-1\/2.right-8 {
      position: relative;
      top: auto;
      right: auto;
      transform: none;
      margin: 2rem auto;
      max-width: 90%;

      div[class*="w-[420px]"] {
        width: 100% !important;
      }
    }

    // 移动端轮播图文字调整
    .carousel-wrapper {
      .carousel-slide-item {
        .text-center {
          padding: 0 1rem;

          h1 {
            font-size: 2rem !important;
          }

          h2 {
            font-size: 1.5rem !important;
          }

          .flex {
            flex-direction: column;
            align-items: center;

            .text-3xl {
              font-size: 1.25rem !important;
              margin-bottom: 0.5rem;
            }

            span {
              margin-left: 0 !important;
              font-size: 1rem !important;
            }
          }
        }
      }
    }

    .@{prefix-cls}-form {
      background-color: transparent;
    }
  }

  // Logo样式
  .@{logo-prefix-cls} {
    &__title {
      font-size: 20px;
      color: #fff;
      font-weight: 600;
    }

    img {
      width: 40px;
      height: 40px;
    }
  }

  // 表单输入框样式
  input:not([type='checkbox']) {
    min-width: 300px;

    @media (max-width: @screen-lg) {
      min-width: 260px;
    }

    @media (max-width: @screen-md) {
      min-width: 240px;
    }

    @media (max-width: @screen-sm) {
      min-width: 200px;
    }
  }

  .@{countdown-prefix-cls} input {
    min-width: unset;
  }

  // 登录方式图标
  &-sign-in-way {
    .anticon {
      font-size: 22px;
      color: #888;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: @primary-color;
      }
    }
  }
}

// Ant Design Carousel 样式定制
.carousel-wrapper {
  height: 600px; // PC端固定高度600px

  .slick-slide {
    height: 600px;
  }

  .slick-list,
  .slick-track {
    height: 100%;
  }

  .carousel-slide-item {
    height: 600px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }



    // 文字覆盖层样式
    .absolute.inset-0 {
      z-index: 2;

      .text-center {
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.7);

        h1 {
          font-size: 3rem;
          font-weight: 700;
          margin-bottom: 1rem;
          line-height: 1.2;
        }

        h2 {
          font-size: 2.25rem;
          font-weight: 700;
          margin-bottom: 1.5rem;
          line-height: 1.2;
        }

        .flex {
          .text-3xl {
            font-size: 1.875rem;
            font-weight: 700;
          }

          span {
            font-size: 1.125rem;
            padding: 0.5rem 1rem;
          }
        }
      }
    }
  }

  // 自定义箭头样式
  .custom-slick-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;

    .anticon {
      font-size: 24px;
      color: #fff;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.5);
      transform: translateY(-50%) scale(1.1);
    }
  }

  // 自定义指示器样式
  :deep(.slick-dots) {
    bottom: 20px !important;
    z-index: 3;

    li {
      width: 12px;
      height: 12px;
      margin: 0 4px;

      button {
        width: 12px !important;
        height: 12px !important;
        border-radius: 50% !important;
        background: rgba(255, 255, 255, 0.5) !important;
        border: none !important;
        opacity: 1 !important;

        &:before {
          display: none !important;
        }
      }

      &.slick-active button {
        background: rgba(255, 255, 255, 1) !important;
      }
    }
  }

  // Transition动画效果
  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.3s ease;
  }

  .fade-enter-from {
    opacity: 0;
    transform: translateY(10px);
  }

  .fade-leave-to {
    opacity: 0;
    transform: translateY(-10px);
  }

  // 登录页面Logo样式
  .login-logo {
    .logo-image {
      height: 40px !important;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) brightness(1.1) !important;

      // 如果需要在白色背景下显示，可以添加背景
      // background: rgba(255, 255, 255, 0.1);
      // border-radius: 4px;
      // padding: 4px;
    }
  }
}

// 暗色主题适配
html[data-theme='dark'] {
  .@{prefix-cls} {

    .ant-input,
    .ant-input-password {
      background-color: #232a3b;
      border-color: #4a5569;
      color: #fff;
    }

    .ant-btn:not(.ant-btn-link):not(.ant-btn-primary) {
      border: 1px solid #4a5569;
      color: #fff;
    }

    &-form {
      background: rgba(0, 0, 0, 0.8) !important;
      backdrop-filter: blur(10px);
    }

    .app-iconify {
      color: #fff;
    }
  }

  .ant-divider-inner-text {
    color: @text-color-secondary;
  }
}
</style>
