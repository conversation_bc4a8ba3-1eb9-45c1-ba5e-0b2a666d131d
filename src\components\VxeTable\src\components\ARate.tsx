import { VxeGlobalRendererOptions } from 'vxe-table';
import {
  createEditRender,
  createDefaultR<PERSON>,
  createFilter<PERSON><PERSON>,
  createDefaultFilterRender,
  createForm<PERSON>temRender,
} from './common';

export default {
  renderTableDefault: createDefaultRender(),
  renderTableEdit: createEditRender(),
  renderTableFilter: createFilterRender(),
  tableFilterDefaultMethod: createDefaultFilterRender(),
  renderFormItemContent: createFormItemRender(),
} as VxeGlobalRendererOptions;
