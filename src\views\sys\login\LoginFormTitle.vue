<template>
  <div class="mt-8 text-2xl font-bold text-center xl:text-3xl enter-x">
    {{ getFormTitle }}
  </div>
</template>

<script lang="ts" setup>
  import { computed, unref } from 'vue';
  import { LoginStateEnum, useLoginState } from './useLogin';
  import { useI18n } from '@/hooks/web/useI18n';

  const { t } = useI18n();

  const { getLoginState } = useLoginState();

  const getFormTitle = computed(() => {
    const titleObj = {
      [LoginStateEnum.RESET_PASSWORD]: t('sys.login.forgetFormTitle'),
      [LoginStateEnum.LOGIN]: t('sys.login.signInFormTitle'),
      [LoginStateEnum.REGISTER]: t('sys.login.signUpFormTitle'),
      [LoginStateEnum.MOBILE]: t('sys.login.mobileSignInFormTitle'),
      [LoginStateEnum.QR_CODE]: t('sys.login.qrSignInFormTitle'),
    };
    return titleObj[unref(getLoginState)];
  });
</script>
