import { VxeGlobalRendererOptions } from 'vxe-table';
import {
  createEditRender,
  createDefaultR<PERSON>,
  createFilter<PERSON><PERSON>,
  createDefaultFilterRender,
  createForm<PERSON>temRender,
} from './common';

export default {
  tableAutoFocus: 'input.ant-input',
  renderTableDefault: createDefaultRender(),
  renderTableEdit: createEditRender(),
  renderTableFilter: createFilter<PERSON>ender(),
  tableFilterDefaultMethod: createDefaultFilterRender(),
  renderFormItemContent: createFormItemRender(),
} as VxeGlobalRendererOptions;
