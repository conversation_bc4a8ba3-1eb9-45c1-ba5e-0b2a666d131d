import { VxeGlobalRendererOptions } from 'vxe-table';
import {
  createEditRender,
  createFilter<PERSON><PERSON>,
  createForm<PERSON>temR<PERSON>,
  createDefaultFilterRender,
  createDefaultRender,
} from './common';

export default {
  tableAutoFocus: 'input.ant-input-number-input',
  renderTableDefault: createDefaultRender(),
  renderTableEdit: createEditRender(),
  renderTableFilter: createFilter<PERSON>ender(),
  tableFilterDefaultMethod: createDefaultFilterRender(),
  renderFormItemContent: createFormItemRender(),
} as VxeGlobalRendererOptions;
