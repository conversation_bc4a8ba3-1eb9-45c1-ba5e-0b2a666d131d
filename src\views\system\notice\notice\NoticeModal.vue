<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    :width="800"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script setup lang="ts">
  import { computed, ref, unref } from 'vue';
  import { formSchema } from './notice.data';
  import { useMessage } from '@/hooks/web/useMessage';
  import { addNoticeApi, editNoticeApi, getNoticeApi } from '@/api/system/notice/notice.api';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { NoticeIM } from '@/model/system/notice';

  const emit = defineEmits(['success', 'register']);
  const { createMessage } = useMessage();
  const isUpdate = ref(true);

  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 80,
    schemas: formSchema,
    showActionButtonGroup: false,
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      const notice = await getNoticeApi(data.record.id);
      setFieldsValue({ ...notice });
    }
  });

  /** 标题初始化 */
  const getTitle = computed(() => (!unref(isUpdate) ? '新增通知公告' : '编辑通知公告'));

  /** 提交按钮 */
  async function handleSubmit() {
    try {
      const values: NoticeIM = await validate();
      setModalProps({ confirmLoading: true });
      unref(isUpdate)
        ? await editNoticeApi(values).then(() => {
            closeModal();
            createMessage.success('编辑通知公告成功！');
          })
        : await addNoticeApi(values).then(() => {
            closeModal();
            createMessage.success('新增通知公告成功！');
          });
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
