{"name": "@xueyi/hooks", "version": "3.3.5", "sideEffects": false, "type": "module", "exports": {".": {"default": "./src/index.ts"}}, "main": "./src/index.ts", "module": "./src/index.ts", "files": ["dist"], "scripts": {"//build": "pnpm unbuild", "//stub": "pnpm unbuild --stub", "clean": "pnpm rimraf .turbo node_modules dist", "lint": "pnpm eslint ."}, "dependencies": {"@vueuse/core": "^10.9.0", "lodash-es": "^4.17.21", "vue": "^3.4.25"}, "devDependencies": {"@xueyi/types": "workspace:*"}}