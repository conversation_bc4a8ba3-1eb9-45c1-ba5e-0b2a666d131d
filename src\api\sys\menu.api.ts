import { defHttp } from '@/utils/http/axios';
import { GetMenuLM } from '@/model/sys';
import { ModuleLM } from '@/model/system/authority';

enum Api {
  GetMenuList = '/system/admin/menu/getRouters/',
  GetModuleList = '/system/admin/module/getRouters',
}

/**
 * @description: Get user menu based on id
 */
export const getMenuList = (moduleId: string) => {
  return defHttp.get<GetMenuLM>({
    url: Api.GetMenuList,
    params: moduleId,
  });
};

/**
 * @description: Get user module based
 */
export const getModuleList = () => {
  return defHttp.get<ModuleLM>({
    url: Api.GetModuleList,
  });
};
